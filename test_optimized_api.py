#!/usr/bin/env python3
"""
测试优化后的API接口
验证CSV文件已被移除，数据直接在JSON中返回
"""

import json
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from api import run_simulation_api, get_default_config


def test_optimized_api():
    """测试优化后的API"""
    print("="*60)
    print("测试优化后的光电对抗仿真API")
    print("="*60)
    
    # 使用默认配置进行测试
    test_config = get_default_config()
    
    # 修改配置以加快测试速度
    test_config["simulation"]["data_count"] = 3
    test_config["simulation"]["duration"] = 5.0
    test_config["simulation"]["output_types"] = ["parameters"]  # 只生成参数数据
    
    print("测试配置:")
    print(f"  数据数量: {test_config['simulation']['data_count']}")
    print(f"  仿真时长: {test_config['simulation']['duration']}秒")
    print(f"  输出类型: {test_config['simulation']['output_types']}")
    print()
    
    try:
        print("开始执行仿真...")
        result_json = run_simulation_api(
            config_input=test_config,
            log_level='INFO'
        )
        
        # 解析JSON结果
        result = json.loads(result_json)
        
        print(f"仿真结果: {'成功' if result['success'] else '失败'}")
        
        if result['success']:
            print(f"输出目录: {result['session_info']['output_directory']}")
            print(f"执行时间: {result['session_info']['duration']:.2f} 秒")
            print(f"生成文件数: {result['output_structure']['total_files']}")
            
            # 检查仿真数据是否直接包含在JSON中
            simulation_results = result.get('simulation_results', {})
            if 'simulation_data' in simulation_results:
                simulation_data = simulation_results['simulation_data']
                print("\n✓ 仿真数据已直接包含在JSON响应中:")
                
                # 检查目标数据
                targets = simulation_data.get('targets', [])
                if targets:
                    print(f"  - 目标数据: {len(targets)} 个设备")
                    target_data = targets[0]
                    if 'parameter_data' in target_data:
                        param_data = target_data['parameter_data']
                        print(f"    * 偏离范围数据: {len(param_data.get('deviation_range', []))} 条")
                        print(f"    * 识别准确率数据: {len(param_data.get('recognition_accuracy', []))} 条")
                        print(f"    * 探测距离数据: {len(param_data.get('detection_range', []))} 条")
                        print(f"    * 探测概率数据: {len(param_data.get('detection_probability', []))} 条")
                
                # 检查干扰数据
                jammers = simulation_data.get('jammers', [])
                if jammers:
                    print(f"  - 干扰数据: {len(jammers)} 个设备")
                    jammer_data = jammers[0]
                    if 'jamming_data' in jammer_data:
                        jamming_data = jammer_data['jamming_data']
                        print(f"    * 干扰效果数据: {len(jamming_data.get('effectiveness', []))} 条")
                        print(f"    * 功耗数据: {len(jamming_data.get('power_consumption', []))} 条")
                        print(f"    * 覆盖范围数据: {len(jamming_data.get('coverage', []))} 条")
                        print(f"    * 持续时间数据: {len(jamming_data.get('duration', []))} 条")
                
                # 检查侦察数据
                recons = simulation_data.get('recons', [])
                if recons:
                    print(f"  - 侦察数据: {len(recons)} 个设备")
                    recon_data = recons[0]
                    if 'reconnaissance_data' in recon_data:
                        recon_data_content = recon_data['reconnaissance_data']
                        print(f"    * 初筛数据: {len(recon_data_content.get('initial_screening', []))} 条")
                        print(f"    * 特征提取数据: {len(recon_data_content.get('feature_extraction', []))} 条")
                        print(f"    * 目标跟踪数据: {len(recon_data_content.get('target_tracking', []))} 条")
                        print(f"    * 识别准确率数据: {len(recon_data_content.get('recognition_accuracy', []))} 条")
                        print(f"    * 探测距离数据: {len(recon_data_content.get('detection_range', []))} 条")
                        print(f"    * 发现概率数据: {len(recon_data_content.get('discovery_probability', []))} 条")
                
                print("\n✓ 优化成功：所有数据已直接包含在JSON响应中，无需CSV文件")
            else:
                print("\n✗ 警告：simulation_data 字段未找到")
            
            # 检查性能指标
            performance = result.get('performance_metrics', {})
            if 'data_statistics' in performance:
                data_stats = performance['data_statistics']
                print(f"\n数据统计:")
                print(f"  总数据条目: {data_stats.get('total_data_entries', 0)}")
                print(f"  数据分类: {data_stats.get('data_breakdown', {})}")
            
        else:
            print(f"错误信息: {result['error_info']}")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "="*60)
    print("✓ API优化测试完成！")
    print("✓ CSV文件已成功移除")
    print("✓ 所有数据现在直接包含在JSON响应中")
    print("="*60)
    return True


if __name__ == "__main__":
    success = test_optimized_api()
    sys.exit(0 if success else 1)
