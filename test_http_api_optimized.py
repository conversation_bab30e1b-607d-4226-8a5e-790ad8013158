#!/usr/bin/env python3
"""
测试优化后的HTTP API接口
验证CSV文件已被移除，数据直接在JSON中返回
"""

import json
import requests
import time
import threading
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from api import get_default_config
import http_api


def start_server():
    """启动HTTP服务器"""
    import uvicorn
    uvicorn.run(
        http_api.app,
        host="127.0.0.1",
        port=8587,
        log_level="warning"  # 减少日志输出
    )


def test_http_api():
    """测试HTTP API"""
    print("="*60)
    print("测试优化后的HTTP API")
    print("="*60)
    
    # 启动服务器
    print("启动HTTP服务器...")
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    # 测试健康检查
    try:
        response = requests.get("http://127.0.0.1:8587/health", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器启动成功")
        else:
            print("✗ 服务器启动失败")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        return False
    
    # 准备测试数据
    test_config = get_default_config()
    test_config["simulation"]["data_count"] = 2
    test_config["simulation"]["duration"] = 3.0
    test_config["simulation"]["output_types"] = ["parameters"]
    
    request_data = {
        "simulation": test_config["simulation"],
        "system": test_config["system"],
        "optical_targets": test_config["optical_targets"],
        "log_level": "INFO"
    }
    
    print("发送仿真请求...")
    print(f"  数据数量: {test_config['simulation']['data_count']}")
    print(f"  仿真时长: {test_config['simulation']['duration']}秒")
    
    try:
        # 发送POST请求
        response = requests.post(
            "http://127.0.0.1:8587/run_simulation",
            json=request_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✓ HTTP请求成功")
            print(f"仿真结果: {'成功' if result['success'] else '失败'}")
            
            if result['success']:
                print(f"执行时间: {result['session_info']['duration']:.2f} 秒")
                print(f"生成文件数: {result['output_structure']['total_files']}")
                
                # 检查仿真数据是否直接包含在JSON中
                simulation_results = result.get('simulation_results', {})
                if 'simulation_data' in simulation_results:
                    simulation_data = simulation_results['simulation_data']
                    print("\n✓ HTTP API返回的数据已直接包含在JSON响应中:")
                    
                    # 检查目标数据
                    targets = simulation_data.get('targets', [])
                    if targets:
                        print(f"  - 目标数据: {len(targets)} 个设备")
                        target_data = targets[0]
                        if 'parameter_data' in target_data:
                            param_data = target_data['parameter_data']
                            print(f"    * 偏离范围数据: {len(param_data.get('deviation_range', []))} 条")
                            print(f"    * 识别准确率数据: {len(param_data.get('recognition_accuracy', []))} 条")
                            print(f"    * 探测距离数据: {len(param_data.get('detection_range', []))} 条")
                            print(f"    * 探测概率数据: {len(param_data.get('detection_probability', []))} 条")
                    
                    print("\n✓ HTTP API优化成功：所有数据已直接包含在JSON响应中")
                else:
                    print("\n✗ 警告：simulation_data 字段未找到")
                
                # 检查性能指标
                performance = result.get('performance_metrics', {})
                if 'data_statistics' in performance:
                    data_stats = performance['data_statistics']
                    print(f"\n数据统计:")
                    print(f"  总数据条目: {data_stats.get('total_data_entries', 0)}")
                    print(f"  数据分类: {data_stats.get('data_breakdown', {})}")
                
                return True
            else:
                print(f"✗ 仿真失败: {result['error_info']}")
                return False
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False


if __name__ == "__main__":
    try:
        success = test_http_api()
        if success:
            print("\n" + "="*60)
            print("✓ HTTP API优化测试完成！")
            print("✓ CSV文件已成功移除")
            print("✓ 所有数据现在直接通过HTTP API在JSON响应中返回")
            print("="*60)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
