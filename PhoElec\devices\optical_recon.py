"""
光电侦察设备仿真模块
实现光电侦察设备的图像识别、特征提取、目标跟踪等功能
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import cv2
from PIL import Image, ImageDraw, ImageFont

from ..core.config_manager import OpticalReconConfig, SystemConfig
from ..physics.detection import ImagingSensor, PhotoDetector
from ..physics.atmosphere import AtmosphericTransmission
from ..physics.constants import WAVELENGTH_RANGES, DETECTOR_PARAMS
from ..utils.logger import LoggerMixin, log_execution_time


logger = logging.getLogger(__name__)


class OpticalReconSimulator(LoggerMixin):
    """光电侦察仿真器"""
    
    def __init__(
        self,
        config: OpticalReconConfig,
        system_config: SystemConfig,
        environment: Dict[str, Any]
    ):
        """
        初始化光电侦察仿真器
        
        Args:
            config: 光电侦察配置
            system_config: 系统配置
            environment: 环境参数
        """
        self.config = config
        self.system_config = system_config
        self.environment = environment
        
        # 确定侦察设备类型
        self.recon_type = self._determine_recon_type()
        
        # 初始化物理模型
        self.atmosphere_model = AtmosphericTransmission(
            environment.get('weather_condition', 'clear_weather')
        )
        
        # 初始化传感器
        self.imaging_sensor = ImagingSensor(self._build_sensor_config())
        self.detector = PhotoDetector(self._get_detector_type(), self._build_detector_config())
        
        # 性能参数
        self.detection_range = config.performance_params.get('detection_range', 15000)  # m
        self.resolution = config.performance_params.get('resolution', 0.05)  # mrad
        self.spectral_coverage = config.performance_params.get('spectral_coverage', (3e-6, 12e-6))
        
        # 算法参数
        self.detection_threshold = 0.7
        self.tracking_accuracy = 0.9
        self.false_alarm_rate = 0.05
        
        self.logger.info(f"初始化光电侦察仿真器: {config.model} ({self.recon_type})")
    
    def _determine_recon_type(self) -> str:
        """确定侦察设备类型"""
        model_lower = self.config.model.lower()
        detection_mode = self.config.detection_mode.lower()
        
        if 'infrared' in model_lower or 'ir' in model_lower or 'infrared' in detection_mode:
            return 'infrared_detector'
        elif 'laser' in model_lower or 'laser' in detection_mode:
            return 'laser_warning'
        elif 'optical' in model_lower or 'electro' in model_lower:
            return 'electro_optical'
        elif 'spectrum' in model_lower or 'spectral' in model_lower:
            return 'spectral_analyzer'
        else:
            return 'multi_spectral'
    
    def _get_detector_type(self) -> str:
        """获取探测器类型"""
        if self.recon_type == 'infrared_detector':
            return 'mercury_cadmium_telluride'
        elif self.recon_type == 'laser_warning':
            return 'indium_gallium_arsenide'
        else:
            return 'silicon'
    
    def _build_sensor_config(self) -> Dict[str, Any]:
        """构建传感器配置"""
        if self.recon_type == 'infrared_detector':
            config = {
                'resolution': self.system_config.image_resolution,
                'spectral_range': WAVELENGTH_RANGES['mid_infrared'],
                'quantum_efficiency': 0.7,
                'pixel_size': 20e-6,
                'focal_length': 0.2,
                'f_number': 2.0
            }
        elif self.recon_type == 'laser_warning':
            config = {
                'resolution': self.system_config.image_resolution,
                'spectral_range': (1.0e-6, 1.7e-6),
                'quantum_efficiency': 0.85,
                'pixel_size': 15e-6,
                'focal_length': 0.15,
                'f_number': 1.8
            }
        else:
            config = {
                'resolution': self.system_config.image_resolution,
                'spectral_range': WAVELENGTH_RANGES['visible'],
                'quantum_efficiency': 0.9,
                'pixel_size': 5e-6,
                'focal_length': 0.1,
                'f_number': 2.8
            }
        
        return config
    
    def _build_detector_config(self) -> Dict[str, Any]:
        """构建探测器配置"""
        detector_type = self._get_detector_type()
        default_params = DETECTOR_PARAMS.get(detector_type, {})
        
        config = {
            'active_area': 1e-6,  # m²
            'quantum_efficiency': default_params.get('quantum_efficiency', 0.8),
            'dark_current': default_params.get('dark_current', 1e-12),
            'spectral_range': default_params.get('spectral_range', (0.4e-6, 1.1e-6))
        }
        
        return config
    
    @log_execution_time
    def generate_reconnaissance_data(
        self,
        count: int,
        output_manager,
        device_id: int
    ) -> List[str]:
        """
        生成侦察数据

        Args:
            count: 生成数量
            output_manager: 输出管理器
            device_id: 设备ID

        Returns:
            生成的数据文件路径列表
        """
        self.logger.info(f"开始生成 {count} 条侦察数据")

        # 生成不同类型的侦察数据
        initial_screening_data = self._generate_initial_screening_data(count)
        feature_extraction_data = self._generate_feature_extraction_data(count)
        target_tracking_data = self._generate_target_tracking_data(count)
        recognition_accuracy_data = self._generate_recognition_accuracy_data(count)
        detection_range_data = self._generate_detection_range_data(count)
        discovery_probability_data = self._generate_discovery_probability_data(count)

        # 构建综合侦察数据
        comprehensive_data = {
            'device_info': {
                'device_id': device_id,
                'model': self.config.model,
                'device_type': 'optical_recon',
                'recon_type': self.recon_type,
                'detection_mode': self.config.detection_mode,
                'work_mode': self.config.work_mode,
                'generation_timestamp': datetime.now().isoformat()
            },
            'reconnaissance_data': {
                'initial_screening': initial_screening_data,
                'feature_extraction': feature_extraction_data,
                'target_tracking': target_tracking_data,
                'recognition_accuracy': recognition_accuracy_data,
                'detection_range': detection_range_data,
                'discovery_probability': discovery_probability_data
            },
            'performance_summary': {
                'max_detection_range_m': self.detection_range,
                'resolution_mrad': self.resolution,
                'spectral_coverage_m': self.spectral_coverage,
                'detection_threshold': self.detection_threshold,
                'tracking_accuracy': self.tracking_accuracy,
                'false_alarm_rate': self.false_alarm_rate
            },
            'sensor_specifications': {
                'detector_type': self._get_detector_type(),
                'image_resolution': self.system_config.image_resolution,
                'spectral_range': self.detector.spectral_range,
                'quantum_efficiency': self.detector.quantum_efficiency
            },
            'environmental_factors': {
                'weather_condition': self.environment.get('weather_condition', 'clear'),
                'temperature_k': self.environment.get('temperature', 288.15),
                'humidity': self.environment.get('humidity', 0.5),
                'atmospheric_visibility_m': self.environment.get('visibility', 23000)
            },
            'statistics': {
                'total_samples': count,
                'data_categories': 6,
                'sample_count_per_category': {
                    'initial_screening': len(initial_screening_data),
                    'feature_extraction': len(feature_extraction_data),
                    'target_tracking': len(target_tracking_data),
                    'recognition_accuracy': len(recognition_accuracy_data),
                    'detection_range': len(detection_range_data),
                    'discovery_probability': len(discovery_probability_data)
                }
            }
        }

        # 保存综合数据文件
        file_paths = []
        comprehensive_path = output_manager.save_json_data(
            comprehensive_data, 'data', f'recon_{device_id}_comprehensive'
        )
        file_paths.append(comprehensive_path)

        self.logger.info(f"侦察数据生成完成，共 {len(file_paths)} 个文件")
        return file_paths
    
    def _generate_initial_screening_data(self, count: int) -> List[Dict[str, Any]]:
        """生成初筛数据"""
        data = []
        
        for i in range(count):
            # 模拟目标检测
            target_present = np.random.choice([True, False], p=[0.3, 0.7])
            
            # 信号强度
            if target_present:
                signal_strength = np.random.uniform(0.5, 1.0)
            else:
                signal_strength = np.random.uniform(0.0, 0.4)
            
            # 噪声水平
            noise_level = np.random.uniform(0.05, 0.2)
            
            # 信噪比
            snr = signal_strength / noise_level if noise_level > 0 else 0
            
            # 检测结果
            detected = snr > self.detection_threshold
            
            # 虚警和漏检
            if target_present and not detected:
                result_type = 'miss'
            elif not target_present and detected:
                result_type = 'false_alarm'
            elif target_present and detected:
                result_type = 'hit'
            else:
                result_type = 'correct_rejection'
            
            data.append({
                'sample_id': i,
                'target_present': target_present,
                'signal_strength': round(signal_strength, 3),
                'noise_level': round(noise_level, 3),
                'snr_db': round(20 * np.log10(snr) if snr > 0 else -60, 1),
                'detected': detected,
                'result_type': result_type,
                'recon_type': self.recon_type,
                'timestamp': datetime.now().isoformat()
            })
        
        return data

    def _generate_feature_extraction_data(self, count: int) -> List[Dict[str, Any]]:
        """生成特征提取数据"""
        data = []

        for i in range(count):
            # 特征类型
            feature_types = ['spectral', 'spatial', 'temporal', 'polarization']
            extracted_features = np.random.choice(feature_types, size=np.random.randint(1, 4), replace=False)

            # 特征质量
            feature_quality = {}
            for feature_type in extracted_features:
                if feature_type == 'spectral':
                    quality = np.random.uniform(0.6, 0.95)
                elif feature_type == 'spatial':
                    quality = np.random.uniform(0.7, 0.9)
                elif feature_type == 'temporal':
                    quality = np.random.uniform(0.5, 0.8)
                else:  # polarization
                    quality = np.random.uniform(0.4, 0.7)

                feature_quality[feature_type] = round(quality, 3)

            # 整体特征置信度
            overall_confidence = np.mean(list(feature_quality.values()))

            # 处理时间
            processing_time = np.random.uniform(0.1, 2.0)  # 秒

            data.append({
                'sample_id': i,
                'extracted_features': list(extracted_features),
                'feature_quality': feature_quality,
                'overall_confidence': round(overall_confidence, 3),
                'processing_time_s': round(processing_time, 3),
                'recon_type': self.recon_type,
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_target_tracking_data(self, count: int) -> List[Dict[str, Any]]:
        """生成目标跟踪数据"""
        data = []

        for i in range(count):
            # 目标运动参数
            target_speed = np.random.uniform(10, 300)  # m/s
            target_direction = np.random.uniform(0, 360)  # degrees

            # 跟踪精度
            base_accuracy = self.tracking_accuracy
            speed_factor = max(0.5, 1.0 - (target_speed - 10) / 290 * 0.3)
            distance_factor = np.random.uniform(0.8, 1.0)

            tracking_accuracy = base_accuracy * speed_factor * distance_factor
            tracking_accuracy = max(0.3, min(0.99, tracking_accuracy))

            # 位置误差
            position_error = np.random.uniform(1, 20)  # meters
            velocity_error = np.random.uniform(0.5, 5)  # m/s

            # 跟踪状态
            track_states = ['acquiring', 'tracking', 'lost', 'coasting']
            track_state = np.random.choice(track_states, p=[0.1, 0.7, 0.1, 0.1])

            # 跟踪持续时间
            track_duration = np.random.uniform(5, 300)  # seconds

            data.append({
                'sample_id': i,
                'target_speed_ms': round(target_speed, 1),
                'target_direction_deg': round(target_direction, 1),
                'tracking_accuracy': round(tracking_accuracy, 3),
                'position_error_m': round(position_error, 1),
                'velocity_error_ms': round(velocity_error, 2),
                'track_state': track_state,
                'track_duration_s': round(track_duration, 1),
                'speed_factor': round(speed_factor, 3),
                'distance_factor': round(distance_factor, 3),
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_recognition_accuracy_data(self, count: int) -> List[Dict[str, Any]]:
        """生成识别准确率数据"""
        data = []

        for i in range(count):
            # 目标类型
            target_types = ['aircraft', 'missile', 'vehicle', 'ship', 'unknown']
            true_type = np.random.choice(target_types)
            recognized_type = np.random.choice(target_types)

            # 识别置信度
            if true_type == recognized_type:
                confidence = np.random.uniform(0.7, 0.95)
                correct_recognition = True
            else:
                confidence = np.random.uniform(0.3, 0.8)
                correct_recognition = False

            # 影响因子
            distance = np.random.uniform(1000, self.detection_range)
            weather_factor = np.random.uniform(0.7, 1.0)
            target_size_factor = np.random.uniform(0.5, 1.5)

            # 距离影响
            distance_factor = max(0.4, 1.0 - (distance - 1000) / (self.detection_range - 1000) * 0.5)

            # 最终识别准确率
            recognition_accuracy = confidence * distance_factor * weather_factor
            recognition_accuracy = max(0.1, min(0.99, recognition_accuracy))

            data.append({
                'sample_id': i,
                'true_type': true_type,
                'recognized_type': recognized_type,
                'correct_recognition': correct_recognition,
                'confidence': round(confidence, 3),
                'recognition_accuracy': round(recognition_accuracy, 3),
                'distance_m': round(distance, 1),
                'distance_factor': round(distance_factor, 3),
                'weather_factor': round(weather_factor, 3),
                'target_size_factor': round(target_size_factor, 3),
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_detection_range_data(self, count: int) -> List[Dict[str, Any]]:
        """生成探测距离数据"""
        data = []

        for i in range(count):
            # 基础探测距离
            base_range = self.detection_range

            # 环境影响
            weather_condition = self.environment.get('weather_condition', 'clear_weather')
            if weather_condition == 'clear_weather':
                weather_factor = np.random.uniform(0.9, 1.0)
            elif weather_condition == 'haze':
                weather_factor = np.random.uniform(0.6, 0.8)
            elif weather_condition == 'fog':
                weather_factor = np.random.uniform(0.3, 0.5)
            else:
                weather_factor = np.random.uniform(0.7, 0.9)

            # 目标特性影响
            target_signature = np.random.uniform(0.3, 1.5)

            # 传感器性能影响
            sensor_performance = np.random.uniform(0.8, 1.1)

            # 计算实际探测距离
            actual_range = base_range * weather_factor * target_signature * sensor_performance
            actual_range = max(500, min(base_range * 1.3, actual_range))

            data.append({
                'sample_id': i,
                'base_range_m': base_range,
                'weather_factor': round(weather_factor, 3),
                'target_signature': round(target_signature, 3),
                'sensor_performance': round(sensor_performance, 3),
                'actual_range_m': round(actual_range, 1),
                'weather_condition': weather_condition,
                'recon_type': self.recon_type,
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_discovery_probability_data(self, count: int) -> List[Dict[str, Any]]:
        """生成发现概率数据"""
        data = []

        for i in range(count):
            # 目标距离
            distance = np.random.uniform(500, self.detection_range * 1.5)

            # 基于距离的发现概率
            if distance <= self.detection_range * 0.3:
                base_probability = 0.98
            elif distance <= self.detection_range * 0.7:
                base_probability = 0.9 - 0.3 * (distance - self.detection_range * 0.3) / (self.detection_range * 0.4)
            elif distance <= self.detection_range:
                base_probability = 0.6 - 0.4 * (distance - self.detection_range * 0.7) / (self.detection_range * 0.3)
            else:
                base_probability = 0.2 * np.exp(-(distance - self.detection_range) / (self.detection_range * 0.5))

            # 环境影响
            weather_factor = np.random.uniform(0.7, 1.0)
            atmospheric_factor = np.random.uniform(0.8, 1.0)

            # 目标特性影响
            target_visibility = np.random.uniform(0.4, 1.2)

            # 传感器状态影响
            sensor_condition = np.random.uniform(0.9, 1.0)

            # 计算最终发现概率
            discovery_probability = (base_probability * weather_factor *
                                   atmospheric_factor * target_visibility * sensor_condition)
            discovery_probability = max(0.01, min(0.99, discovery_probability))

            data.append({
                'sample_id': i,
                'distance_m': round(distance, 1),
                'base_probability': round(base_probability, 3),
                'weather_factor': round(weather_factor, 3),
                'atmospheric_factor': round(atmospheric_factor, 3),
                'target_visibility': round(target_visibility, 3),
                'sensor_condition': round(sensor_condition, 3),
                'discovery_probability': round(discovery_probability, 3),
                'recon_type': self.recon_type,
                'timestamp': datetime.now().isoformat()
            })

        return data


